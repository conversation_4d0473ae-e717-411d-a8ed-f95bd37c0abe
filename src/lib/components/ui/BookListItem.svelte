<script lang="ts">
    import BookOpenSvg from '$lib/components/svg/BookOpenSvg.svelte';
    import GiftSvg from '$lib/components/svg/GiftSvg.svelte';
    import LanguageSvg from '$lib/components/svg/LanguageSvg.svelte';
    import TrashSvg from '$lib/components/svg/TrashSvg.svelte';
    import type Book from '$lib/domain/Book';
    import type SearchBook from '$lib/domain/SearchBook';
    import {t} from '$lib/localization/Localization';
    import AppRoutes from '$routes/AppRoutes';

    export let book: Book | SearchBook;
    export let deleteCallback: ((bookIdentifier: string) => void) | undefined = undefined;
</script>

<a href={AppRoutes.book(book.uuid)} class="group block">
    <div
        class="relative flex h-full flex-col rounded-lg border border-gray-200 bg-gray-50 p-3 shadow transition-all hover:bg-gray-100 hover:shadow-md dark:border-gray-600 dark:bg-gray-700 dark:hover:bg-gray-600"
    >
        {#if deleteCallback !== undefined}
            <button
                type="button"
                class="absolute right-2 top-2 z-10 rounded-full p-1 opacity-0 transition-opacity hover:bg-gray-200 group-hover:opacity-100 dark:hover:bg-gray-600"
                on:click={(event) => {
                    event.preventDefault();
                    event.stopPropagation();
                    deleteCallback(book.uuid);
                }}
            >
                <TrashSvg />
            </button>
        {/if}

        <!-- Book Cover -->
        <div class="mb-3 flex justify-center">
            {#if book.cover}
                <img src={book.cover} class="h-24 w-16 rounded-lg object-cover shadow-sm" alt="Book cover" />
            {:else}
                <div class="flex h-24 w-16 items-center justify-center rounded-lg bg-gray-200 shadow-sm dark:bg-gray-600">
                    <BookOpenSvg svgClass="h-8 w-8 text-gray-400 dark:text-gray-500" />
                </div>
            {/if}
        </div>

        <!-- Book Info -->
        <div class="flex flex-1 flex-col space-y-2">
            <div class="text-center">
                <h3 class="line-clamp-2 text-sm font-semibold leading-tight text-gray-700 dark:text-white">
                    {book.title}
                </h3>
                <p class="mt-1 line-clamp-1 text-xs text-gray-500 dark:text-gray-400">
                    {book.authors.join(', ')}
                </p>
            </div>

            <!-- Metadata -->
            <div class="mt-auto space-y-1">
                <div class="flex items-center justify-center text-xs text-gray-500 dark:text-gray-400">
                    <LanguageSvg svgClass="mr-1 h-3 w-3" />
                    <span class="line-clamp-1">{book.languages.map((language) => language.text).join(', ')}</span>
                </div>
                {#if book.isFree}
                    <div class="flex items-center justify-center text-xs text-gray-500 dark:text-gray-400">
                        <GiftSvg svgClass="mr-1 h-3 w-3" />
                        <span>{$t('book.read')}</span>
                    </div>
                {/if}
            </div>
        </div>
    </div>
</a>
