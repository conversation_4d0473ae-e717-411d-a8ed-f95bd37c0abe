<script lang="ts">
    import {onDestroy, onMount} from 'svelte';
    import BookListItem from '$lib/components/ui/BookListItem.svelte';
    import type Book from '$lib/domain/Book';
    import type SearchBook from '$lib/domain/SearchBook';

    export let books: Book[] | SearchBook[] = [];
    export let getBooks: ((page) => Promise<Book[] | SearchBook[]>) | undefined = undefined;
    export let deleteCallback: ((bookIdentifier: string) => void) | undefined = undefined;

    const activateScrollBeforePixels = 1000;
    let page = 1;
    let isScrollBeingHandled = false;

    async function onScroll() {
        if (getBooks === undefined || isScrollBeingHandled) {
            return;
        }

        isScrollBeingHandled = true;

        const {scrollTop, clientHeight, scrollHeight} = document.documentElement;
        if (scrollTop + clientHeight >= scrollHeight - activateScrollBeforePixels) {
            const nextPageBooks = await getBooks(page);
            books = [...books, ...nextPageBooks];
            page++;

            if (nextPageBooks.length === 0) {
                window.removeEventListener('scroll', onScroll);
            }
        }

        isScrollBeingHandled = false;
    }

    onMount(async () => {
        if (getBooks === undefined) {
            return;
        }

        books = await getBooks(page);
        page++;

        window.addEventListener('scroll', onScroll);
    });

    onDestroy(() => {
        if (typeof window !== 'undefined') {
            window.removeEventListener('scroll', onScroll);
        }
    });
</script>

<div class="grid grid-cols-2 gap-4 sm:grid-cols-3  lg:grid-cols-4 2xl:grid-cols-5">
    {#each books as book, i (i)}
        <BookListItem book={book} deleteCallback={deleteCallback} />
    {/each}
</div>
