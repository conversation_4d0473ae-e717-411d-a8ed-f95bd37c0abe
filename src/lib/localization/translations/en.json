{"accountSettings": {"accountSettings": "Account settings", "accountSettingsSaved": "Account settings have been saved.", "alarm": "Alarm", "changePassword": "Change password", "changeProfilePicture": "Change profile picture", "currentPassword": "Current password", "email": "Email:", "enableAlarm": "Enable read alarm", "imageUpload": "Image upload", "language": "Language:", "name": "Name:", "newPassword": "New password", "passwordSaved": "Your new password has been saved.", "readAlarm": "Read alarm", "readNotificationQuote1": "\"A reader lives a thousand lives before he dies. The man who never reads lives only one.\" - <PERSON>", "readNotificationQuote2": "\"A book is a gift you can open again and again.\" - <PERSON>", "readNotificationQuote3": "\"Never put off till tomorrow the book you can read today.\" - <PERSON><PERSON><PERSON>", "readNotificationTitle": "Reading alarm", "savePassword": "Save", "saveSettings": "Save", "setAlarm": "Set a reading alarm. You will receive a reminder notification at that time.", "verificationEmailSent": "An email has been sent to your email address. Please use the link to verify your new account."}, "achievements": {"achievements": "Achievements"}, "addList": {"addList": "Add list", "error": "There was an error. Please try again.", "listName": "List Name", "nameLengthError": "Name cannot be longer than 200 letters.", "nameRequiredError": "Name is required.", "submit": "Submit"}, "aiChat": {"conversationLimitReached": "Conversation limit reached", "conversationLimitReachedDescription": "This conversation has reached the maximum number of replies. Please start a new conversation to continue chatting.", "dailyLimitReached": "Daily limit reached", "dailyLimitReachedDescription": "You've reached your daily usage limit. Please try again tomorrow to continue the conversation.", "introMessage": "Hi there! 📚\nI’m here to help you find your next great read. Just tell me what you’re in the mood for — a specific genre, vibe, topic, or feeling — and I’ll recommend books with accurate titles, a bit about each one, and why they might be perfect for you.\n\nIf you’re not sure where to start, just share a few things you love, and we’ll discover something wonderful together!", "newChat": "New chat", "now": "now", "typeMessage": "Type your message..."}, "auth": {"backToApp": "Back to app", "changePassword": "Change password", "clickHere": "click here", "confirmPassword": "Confirm password", "emailTaken": "The email has already been taken.", "emailVerification": "Email verification", "emailVerified": "Your email has been verified.", "error": "There was an error.", "forgotPassword": "Forgot password?", "haveAccount": "Already have an account?", "ifForgottenPassword": "If you have forgotten your password", "login": "Log in", "noAccount": "Don't have an account?", "or": "- or -", "passwordChange": "Password change", "passwordChanged": "Your password has been changed.", "passwordConfirmationError": "The passwords don't match.", "resetPassword": "Reset password", "resetPasswordEmail": "Enter your email address and we'll send you an email with instructions to reset your password.", "resetPasswordEmailSent": "If that email account is tied to a Liberom account, an email with a reset password link has been sent.", "setNewPassword": "Set a new password", "signup": "Sign up", "verificationEmailError": "There was an error trying to verify your email.", "verificationEmailSent": "A verification email has been sent to your account. Please use the link to verify your email account."}, "author": {"books": "Books", "follow": "Follow", "subjects": "Subjects:", "unfollow": "Unfollow"}, "book": {"addList": "Add to library", "addTo": "Add to", "audiobook": "Listen for free", "buyAmazon": "Buy from Amazon", "by": "by", "details": "Details", "dislike": "Dislike", "goToBook": "Go to book", "languages": "Languages", "like": "Like", "listen": "Listen", "numberOfPages": "Number of pages", "preview": "Preview", "publishDate": "Publish year", "read": "Read", "readingActivity": "Reading activity", "similarBooks": "Similar Books", "subjects": "Subjects", "summary": "Summary"}, "bookStatuses": {"abandoned": "Abandoned", "currentlyReading": "Currently reading", "dropped": "Abandoned", "finished": "Finished", "read": "Finished", "setReadingStatus": "Set reading status", "wantToRead": "Want to read"}, "datePicker": {"selectDate": "Select a date"}, "fields": {"email": "Email address", "name": "Name", "password": "Password"}, "header": {"accountSettings": "Account settings", "hello": "Hello", "language": "Language", "logout": "Sign out", "search": "Search", "searchPlaceholder": "Search for a book title", "theme": "Switch theme"}, "lists": {"authorsFollowed": "Authors you followed", "createList": "Create new list", "liked": "Liked", "seeAll": "See all"}, "menu": {"accountSettings": "Account settings", "achievements": "Achievements", "addBookList": "Add book list", "author": "Author", "book": "Book", "bookReader": "Book reader", "freeAudiobooks": "Free audiobooks", "home": "Home", "myLibrary": "My library", "recommendations": "Recommendations", "search": "Search", "viewBookList": "View book list"}, "meta": {"description": "Find and read over 700.000 books or listen to over 18.000 audiobooks, all for free. You can also like, dislike, share and organize books (free or otherwise) into lists. You will get custom recommendations based on your likes and favorite authors."}, "notifications": {"genericError": "Something went wrong, please try again."}, "readingActivity": {"addActivity": "Add reading activity", "activityDeleted": "Reading activity deleted successfully.", "activitySaved": "Reading activity saved successfully.", "confirmDelete": "Confirm Delete", "confirmDeleteMessage": "Are you sure you want to delete this reading activity? This action cannot be undone.", "delete": "Delete", "description": "Add information about your reading activity to track your progress. Start and finish dates should mark one complete reading of the book.", "edit": "Edit", "editActivity": "Edit Reading Activity", "endDate": "End Date", "language": "Language", "noActivities": "No reading activities yet. Add your first activity to track your reading progress!", "pages": "Pages", "pagesRead": "Pages Read", "save": "Save", "startDate": "Start Date", "totalPages": "Total Pages", "validationEndDateAfterStart": "End date must be on or after start date.", "validationPagesReadExceedsTotal": "Pages read cannot exceed total pages."}, "recommendations": {"betterRecommendations": "For better recommendations tell us what books you like or dislike so we know your preferences.", "freeBooksLikes": "Free books like the ones you liked", "moreBooksAuthors": "More books from authors you followed", "moreBooksLikes": "More books like the ones you liked", "moreBooksSubjects": "More books from subjects that you enjoy", "popularBooks": "Popular books", "recommendations": "Recommendations", "searchBooks": "Search for books", "topThreeRecommendations": "Top 3 recommendations"}, "search": {"advancedFilters": "Advanced filters", "author": "Author", "defaultSearchTitle": "Trending", "freeBooks": "Show only free books", "from": "From", "isbn": "ISBN", "language": "Language", "publishYear": "Publish year", "search": "Search", "subject": "Subject", "tagline": "Liberom gives you free, bite‑sized summaries of every book so you can discover big ideas in minutes.", "title": "Title", "to": "To"}, "tutorial": {"closePlayer": "You can click on the X button to close the player.", "end": "But we will let you discover those on your own. Don't forget to explore, connect, and share your literary adventures with other book enthusiasts. Happy reading!", "finish": "Finish", "freeAudiobooks": "Enjoy audiobooks from the public domain! Choose an audiobook that you think you’ll enjoy.", "freeBooks": "All books that are in the public domain are available to read. Dive into classic masterpieces and timeless stories and unlock a world of free literature!", "goToAudiobooks": "Now let’s check the free audiobooks section.", "goToRecommended": "Now let’s go to the recommendations page to see what books are recommended for you.", "likeBook": "Rate your favorite books by clicking the 'Like' button, and let us know your preferences for better recommendations! Go ahead and like this book now (you can undo this later).", "listenToFirstChapter": "Now click on the first chapter to start listening.", "nextButton": "Next", "otherFeatures": "You're all set to embark on your reading journey with Liberom! We also have other features such as setting a read status for each book or making custom lists to organize your books better. You can also set an alarm to remind you to read every day.", "readBook": "Now click on the button to read the book for free!", "recommendations": "Explore a curated selection of books just for you! Delve into a personalized list of recommendations based on your reading preferences. The more books you like and dislike, the better the recommendations will be.", "searchForm": "You can use the search form to instantly access a vast collection of books by searching through titles, authors, and other kinds of filters.", "selectBook": "Select a book that appeals to you from the results list.", "welcome": "Welcome to Liberom, your go-to app for discovering, organizing, and enjoying a diverse collection of books. Begin your literary journey by clicking the button below.", "welcomeButton": "Get started", "welcomeTitle": "Welcome"}, "viewList": {"confirmDelete": "Confirm delete", "confirmDeleteQuestion": "Are you sure you want to delete this list?", "delete": "Delete", "listChanged": "List name has been changed.", "listName": "List name", "listUpdate": "List update", "nameLengthError": "Name cannot be longer than 200 letters.", "nameRequiredError": "Name is required.", "update": "Update"}}